from flask import Flask, render_template, request, send_file, make_response
from docxtpl import DocxTemplate
import os
import subprocess
from datetime import datetime

app = Flask(__name__)

UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def converte_moeda_para_float(valor_str):
    """Converte uma string de moeda (R$ 1234.56) para float"""
    try:
        if not valor_str or valor_str.strip() == '':
            return 0.0

        # Remove R$ e espaços
        valor_limpo = valor_str.replace('R$', '').strip()
        # Remove espaços entre os números (usado como separador de milhares)
        valor_limpo = valor_limpo.replace(' ', '')
        # Como o input já vem com ponto, só converte direto para float
        return float(valor_limpo)
    except:
        return 0.0

def formata_moeda_pdf(valor):
    """Formata um número para moeda brasileira no PDF (com vírgula)"""
    try:
        if valor is None or valor == 0:
            return "R$ 0,00"

        # Formata o número com pontos e vírgulas no padrão brasileiro
        valor_str = f"{valor:,.2f}".replace(',', 'TEMP').replace('.', ',').replace('TEMP', '.')
        return f"R$ {valor_str}"
    except:
        return "R$ 0,00"

def calcula_valores(valor_precatorio_str, valor_oferta_str, perc_deducoes_str, perc_honorarios_str, perc_taxa_str,
                   tipo_deducoes, tipo_honorarios, tipo_oferta, tipo_taxa):
    """Calcula todos os valores mantendo a formatação brasileira"""

    # Converte valores monetários para float (já vem com ponto do formulário)
    valor_precatorio = converte_moeda_para_float(valor_precatorio_str)

    # 1. Primeiro calcula honorários
    if tipo_honorarios == 'porcentagem':
        try:
            perc_honorarios = float(perc_honorarios_str.replace('%', '').strip()) / 100
            honorarios = valor_precatorio * perc_honorarios
            honorarios_texto = f"{perc_honorarios * 100:.1f}%"
        except:
            honorarios = 0.0
            honorarios_texto = "0%"
    else:
        honorarios = converte_moeda_para_float(perc_honorarios_str)
        honorarios_texto = formata_moeda_pdf(honorarios)

    # 2. Depois calcula deduções (agora sobre o valor total do precatório)
    if tipo_deducoes == 'porcentagem':
        try:
            perc_deducoes = float(perc_deducoes_str.replace('%', '').strip()) / 100
            deducoes = valor_precatorio * perc_deducoes
            deducoes_texto = f"{perc_deducoes * 100:.2f}%"
        except:
            deducoes = 0.0
            deducoes_texto = "0%"
    else:
        deducoes = converte_moeda_para_float(perc_deducoes_str)
        deducoes_texto = formata_moeda_pdf(deducoes)

    total_liquido1 = valor_precatorio - honorarios - deducoes

    # 3. Calcula oferta
    if tipo_oferta == 'porcentagem':
        try:
            perc_oferta = float(valor_oferta_str.replace('%', '').strip()) / 100
            valor_oferta = total_liquido1 * perc_oferta
            oferta_texto = f"{perc_oferta * 100:.1f}%"
        except:
            valor_oferta = 0.0
            oferta_texto = "0%"
    else:
        valor_oferta = converte_moeda_para_float(valor_oferta_str)
        oferta_texto = formata_moeda_pdf(valor_oferta)

    # 4. Calcula taxa
    if tipo_taxa == 'porcentagem':
        try:
            perc_taxa = float(perc_taxa_str.replace('%', '').strip()) / 100
            taxa = valor_oferta * perc_taxa
            taxa_texto = f"{perc_taxa * 100:.1f}%"
        except:
            taxa = 0.0
            taxa_texto = "0%"
    else:
        taxa = converte_moeda_para_float(perc_taxa_str)
        taxa_texto = formata_moeda_pdf(taxa)

    total_liquido2 = valor_oferta - taxa

    # Retorna dicionário com valores formatados para o PDF (com vírgula)
    return {
        'valorprecatorio': formata_moeda_pdf(valor_precatorio),
        'honorarios': formata_moeda_pdf(honorarios),
        'honorarios_texto': honorarios_texto,
        'deducoeslegais': formata_moeda_pdf(deducoes),
        'deducoes_texto': deducoes_texto,
        'totalliquido1': formata_moeda_pdf(total_liquido1),
        'oferta': formata_moeda_pdf(valor_oferta),
        'oferta_texto': oferta_texto,
        'taxaintermediacao': formata_moeda_pdf(taxa),
        'taxa_texto': taxa_texto,
        'totalliquido2': formata_moeda_pdf(total_liquido2)
    }

def calcula_valores_resumido(valor_oferta_str, perc_taxa_str, tipo_taxa):
    """Calcula valores para proposta resumida, onde a oferta já é um valor fixo"""
    
    # Converte valor da oferta para float
    valor_oferta = converte_moeda_para_float(valor_oferta_str)
    
    # Calcula taxa
    if tipo_taxa == 'porcentagem':
        try:
            perc_taxa = float(perc_taxa_str.replace('%', '').strip()) / 100
            taxa = valor_oferta * perc_taxa
            taxa_texto = f"{perc_taxa * 100:.1f}%"
        except:
            taxa = 0.0
            taxa_texto = "0%"
    else:
        taxa = converte_moeda_para_float(perc_taxa_str)
        taxa_texto = formata_moeda_pdf(taxa)
    
    total_liquido2 = valor_oferta - taxa
    
    # Retorna dicionário com valores formatados para o PDF (com vírgula)
    return {
        'oferta': formata_moeda_pdf(valor_oferta),
        'taxaintermediacao': formata_moeda_pdf(taxa),
        'taxa_texto': taxa_texto,
        'totalliquido2': formata_moeda_pdf(total_liquido2)
    }

def generate_pdf(template_path, context, output_docx, output_pdf):
    doc = DocxTemplate(template_path)
    doc.render(context)
    doc.save(output_docx)

    libreoffice_paths = [
        '/Applications/LibreOffice.app/Contents/MacOS/soffice',
        'soffice',
        'libreoffice',
        '/opt/homebrew/bin/soffice'
    ]

    for soffice in libreoffice_paths:
        try:
            subprocess.run([
                soffice,
                '--headless',
                '--convert-to',
                'pdf',
                '--outdir',
                UPLOAD_FOLDER,
                output_docx
            ], check=True)
            return True
        except:
            continue
    return False

@app.route('/', methods=['GET'])
def index():
    return render_template('index.html')

@app.route('/download', methods=['POST'])
def download():
    try:
        # Dados básicos
        nome = request.form['nome']
        processo = request.form['processo']

        # Valores do formulário (mantendo formatação original)
        valor_precatorio = request.form.get('valor_precatorio', 'R$ 0.00')
        valor_oferta = request.form.get('valor_oferta', '0.0')
        perc_deducoes = request.form.get('perc_deducoes', '0.0')
        perc_honorarios = request.form.get('perc_honorarios', '0.0')
        perc_taxa = request.form.get('perc_taxa', '0.0')

        # Tipos de cálculo (porcentagem ou fixo)
        tipo_deducoes = request.form.get('tipo_deducoes', 'porcentagem')
        tipo_honorarios = request.form.get('tipo_honorarios', 'porcentagem')
        tipo_oferta = request.form.get('tipo_oferta', 'porcentagem')
        tipo_taxa = request.form.get('tipo_taxa', 'porcentagem')

        # Remove espaços dos valores antes de calcular
        valor_precatorio = valor_precatorio.replace(' ', '')

        # Calcula valores
        valores = calcula_valores(
            valor_precatorio,
            valor_oferta,
            perc_deducoes,
            perc_honorarios,
            perc_taxa,
            tipo_deducoes,
            tipo_honorarios,
            tipo_oferta,
            tipo_taxa
        )

        # Contexto para o template
        context = {
            'nome': nome,
            'processo': processo,
            **valores
        }

        # Define nome dos arquivos
        output_docx = os.path.join(UPLOAD_FOLDER, f'proposta_{nome}.docx')
        output_pdf = os.path.join(UPLOAD_FOLDER, f'proposta_{nome}.pdf')

        if not generate_pdf(os.path.join(os.path.dirname(__file__), 'templates', '1.docx'), context, output_docx, output_pdf):
            return "Erro: LibreOffice não encontrado.", 500

        # Lê o arquivo PDF
        with open(output_pdf, 'rb') as pdf_file:
            pdf_content = pdf_file.read()

        # Limpa arquivos temporários
        for file in [output_docx, output_pdf]:
            if os.path.exists(file):
                os.remove(file)

        # Retorna o PDF diretamente
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=proposta_{nome}.pdf'

        return response

    except Exception as e:
        return f"Erro ao gerar documentos: {str(e)}", 500

@app.route('/download_resumido', methods=['POST'])
def download_resumido():
    try:
        # Dados básicos
        nome = request.form['nome']
        processo = request.form['processo']

        # Valores do formulário (mantendo formatação original)
        valor_oferta = request.form.get('valor_oferta', 'R$ 0.00')
        perc_taxa = request.form.get('perc_taxa', '0.0')

        # Tipos de cálculo (porcentagem ou fixo)
        tipo_taxa = request.form.get('tipo_taxa', 'porcentagem')

        # Remove espaços dos valores antes de calcular
        valor_oferta = valor_oferta.replace(' ', '')

        # Calcula valores
        valores = calcula_valores_resumido(
            valor_oferta,
            perc_taxa,
            tipo_taxa
        )

        # Contexto para o template
        context = {
            'nome': nome,
            'processo': processo,
            **valores
        }

        # Define nome dos arquivos
        output_docx = os.path.join(UPLOAD_FOLDER, f'proposta_resumida_{nome}.docx')
        output_pdf = os.path.join(UPLOAD_FOLDER, f'proposta_resumida_{nome}.pdf')

        if not generate_pdf(os.path.join(os.path.dirname(__file__), 'templates', '2.docx'), context, output_docx, output_pdf):
            return "Erro: LibreOffice não encontrado.", 500

        # Lê o arquivo PDF
        with open(output_pdf, 'rb') as pdf_file:
            pdf_content = pdf_file.read()

        # Limpa arquivos temporários
        for file in [output_docx, output_pdf]:
            if os.path.exists(file):
                os.remove(file)

        # Retorna o PDF diretamente
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=proposta_resumida_{nome}.pdf'

        return response

    except Exception as e:
        return f"Erro ao gerar documentos: {str(e)}", 500

@app.route('/save', methods=['POST'])
def save():
    try:
        # Dados básicos
        nome = request.form['nome']
        processo = request.form['processo']

        # Valores do formulário (mantendo formatação original)
        valor_precatorio = request.form.get('valor_precatorio', 'R$ 0.00')
        valor_oferta = request.form.get('valor_oferta', '0.0')
        perc_deducoes = request.form.get('perc_deducoes', '0.0')
        perc_honorarios = request.form.get('perc_honorarios', '0.0')
        perc_taxa = request.form.get('perc_taxa', '0.0')

        # Tipos de cálculo (porcentagem ou fixo)
        tipo_deducoes = request.form.get('tipo_deducoes', 'porcentagem')
        tipo_honorarios = request.form.get('tipo_honorarios', 'porcentagem')
        tipo_oferta = request.form.get('tipo_oferta', 'porcentagem')
        tipo_taxa = request.form.get('tipo_taxa', 'porcentagem')

        # Remove espaços dos valores antes de calcular
        valor_precatorio = valor_precatorio.replace(' ', '')

        # Calcula valores
        valores = calcula_valores(
            valor_precatorio,
            valor_oferta,
            perc_deducoes,
            perc_honorarios,
            perc_taxa,
            tipo_deducoes,
            tipo_honorarios,
            tipo_oferta,
            tipo_taxa
        )

        # Contexto para o template
        context = {
            'nome': nome,
            'processo': processo,
            **valores
        }

        # Define nome dos arquivos
        output_docx = os.path.join(UPLOAD_FOLDER, f'proposta_{nome}.docx')
        output_pdf = os.path.join(UPLOAD_FOLDER, f'proposta_{nome}.pdf')

        if not generate_pdf(os.path.join(os.path.dirname(__file__), 'templates', '1.docx'), context, output_docx, output_pdf):
            return "Erro: LibreOffice não encontrado.", 500

        # Lê o arquivo PDF
        with open(output_pdf, 'rb') as pdf_file:
            pdf_content = pdf_file.read()

        # Limpa arquivos temporários
        for file in [output_docx, output_pdf]:
            if os.path.exists(file):
                os.remove(file)

        # Retorna o PDF diretamente
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=proposta_{nome}.pdf'

        return response

    except Exception as e:
        return f"Erro ao gerar documentos: {str(e)}", 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001)