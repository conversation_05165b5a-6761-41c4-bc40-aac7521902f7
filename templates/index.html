<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerador de Propostas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-maskmoney/3.0.2/jquery.maskMoney.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding-top: 2rem;
            min-height: 100vh;
        }
        .card {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
            border-radius: 15px;
        }
        .card-header {
            background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px 15px 0 0 !important;
        }
        .card-body {
            padding: 2rem;
        }
        .form-label {
            font-weight: 600;
            color: #344767;
            margin-bottom: 0.5rem;
        }
        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        .form-control, .form-select {
            border: 1px solid #ced4da;
            padding: 0.75rem;
            font-size: 1rem;
            border-radius: 10px;
            transition: all 0.2s ease-in-out;
        }
        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
        .input-group {
            border-radius: 10px;
            overflow: hidden;
        }
        .input-group-text {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            color: #495057;
            font-weight: 500;
            padding: 0.75rem;
        }
        .money-input, .percent-input {
            text-align: right;
            font-family: monospace;
            font-size: 1.1rem;
            letter-spacing: 1px;
        }
        .btn-primary {
            padding: 1rem 3rem;
            font-weight: 600;
            font-size: 1.1rem;
            border-radius: 10px;
            background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
            border: none;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(13, 110, 253, 0.4);
        }
        .mb-4 {
            margin-bottom: 2rem !important;
        }
        #downloadFrame {
            display: none;
        }
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        .btn-group .btn {
            flex: 0 1 auto;
            min-width: 200px;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
        }
        .btn-secondary:hover {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
        }
        /* Estilos para as abas */
        .nav-tabs {
            border-bottom: none;
            margin-bottom: 1rem;
        }
        .nav-tabs .nav-link {
            font-weight: 600;
            padding: 1rem 2rem;
            border-radius: 10px 10px 0 0;
            border: none;
            color: #6c757d;
            transition: all 0.3s ease;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
            color: white;
            border: none;
        }
        .nav-tabs .nav-link:hover:not(.active) {
            background-color: #e9ecef;
            border-color: transparent;
        }
        .tab-content {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-center mb-3">Gerador de Propostas</h2>
                        <!-- Abas de navegação -->
                        <ul class="nav nav-tabs" id="propostaTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="completa-tab" data-bs-toggle="tab" data-bs-target="#completa" 
                                    type="button" role="tab" aria-controls="completa" aria-selected="true">
                                    Proposta Completa
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="resumida-tab" data-bs-toggle="tab" data-bs-target="#resumida" 
                                    type="button" role="tab" aria-controls="resumida" aria-selected="false">
                                    Proposta Resumida
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <!-- Conteúdo das abas -->
                        <div class="tab-content" id="propostaTabsContent">
                            <!-- Aba Proposta Completa -->
                            <div class="tab-pane fade show active" id="completa" role="tabpanel" aria-labelledby="completa-tab">
                                <form method="POST" action="/download">
                                    <div class="mb-4">
                                        <label class="form-label">Nome</label>
                                        <input type="text" class="form-control" name="nome" required>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Processo</label>
                                        <input type="text" class="form-control" name="processo" required>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Valor do Precatório/RPV</label>
                                        <div class="input-group">
                                            <span class="input-group-text">R$</span>
                                            <input type="text" class="form-control money money-input" name="valor_precatorio" required>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Honorários Advocatícios</label>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_honorarios" value="porcentagem" id="honorariosPorcentagem" checked>
                                                <label class="form-check-label" for="honorariosPorcentagem">Porcentagem</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_honorarios" value="fixo" id="honorariosFixo">
                                                <label class="form-check-label" for="honorariosFixo">Valor Fixo</label>
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <input type="text" class="form-control percent-input" name="perc_honorarios" required>
                                            <span class="input-group-text unit-text">%</span>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Deduções Legais</label>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_deducoes" value="porcentagem" id="deducoesPorcentagem" checked>
                                                <label class="form-check-label" for="deducoesPorcentagem">Porcentagem</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_deducoes" value="fixo" id="deducoesFixo">
                                                <label class="form-check-label" for="deducoesFixo">Valor Fixo</label>
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <input type="text" class="form-control percent-input" name="perc_deducoes" required>
                                            <span class="input-group-text unit-text">%</span>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Valor da Oferta</label>
                                        <div class="form-text mb-2">Sobre o valor líquido</div>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_oferta" value="porcentagem" id="ofertaPorcentagem" checked>
                                                <label class="form-check-label" for="ofertaPorcentagem">Porcentagem</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_oferta" value="fixo" id="ofertaFixo">
                                                <label class="form-check-label" for="ofertaFixo">Valor Fixo</label>
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <input type="text" class="form-control percent-input" name="valor_oferta" required>
                                            <span class="input-group-text unit-text">%</span>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Taxa de Intermediação</label>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_taxa" value="porcentagem" id="taxaPorcentagem" checked>
                                                <label class="form-check-label" for="taxaPorcentagem">Porcentagem</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_taxa" value="fixo" id="taxaFixo">
                                                <label class="form-check-label" for="taxaFixo">Valor Fixo</label>
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <input type="text" class="form-control percent-input" name="perc_taxa" required>
                                            <span class="input-group-text unit-text">%</span>
                                        </div>
                                    </div>

                                    <div class="text-center">
                                        <div class="btn-group">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                Gerar Proposta Completa
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Aba Proposta Resumida -->
                            <div class="tab-pane fade" id="resumida" role="tabpanel" aria-labelledby="resumida-tab">
                                <form method="POST" action="/download_resumido">
                                    <div class="mb-4">
                                        <label class="form-label">Nome</label>
                                        <input type="text" class="form-control" name="nome" required>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Processo</label>
                                        <input type="text" class="form-control" name="processo" required>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Valor da Oferta</label>
                                        <div class="input-group">
                                            <span class="input-group-text">R$</span>
                                            <input type="text" class="form-control money money-input" name="valor_oferta" required>
                                        </div>
                                        <input type="hidden" name="tipo_oferta" value="fixo">
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Taxa de Intermediação</label>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_taxa" value="porcentagem" id="taxaPorcentagemR" checked>
                                                <label class="form-check-label" for="taxaPorcentagemR">Porcentagem</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="tipo_taxa" value="fixo" id="taxaFixoR">
                                                <label class="form-check-label" for="taxaFixoR">Valor Fixo</label>
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <input type="text" class="form-control percent-input" name="perc_taxa" required>
                                            <span class="input-group-text unit-text">%</span>
                                        </div>
                                    </div>

                                    <div class="text-center">
                                        <div class="btn-group">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                Gerar Proposta Resumida
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Função para formatar números com espaços
            function formatNumber(value) {
                // Remove qualquer formatação existente
                let clean = value.replace(/[^\d.]/g, '');

                // Separa parte inteira e decimal
                let parts = clean.split('.');
                let integerPart = parts[0];
                let decimalPart = parts.length > 1 ? '.' + parts[1] : '';

                // Adiciona espaços a cada 3 dígitos da direita para a esquerda
                let formatted = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');

                return formatted + decimalPart;
            }

            // Configura máscara para valores monetários
            $('.money').maskMoney({
                prefix: '',
                thousands: ' ',  // Usando espaço como separador de milhares
                decimal: '.',
                allowZero: true,
                precision: 2,
                allowNegative: false
            });

            // Configura máscara para percentuais
            $('.percent-input').maskMoney({
                suffix: '',
                thousands: '',
                decimal: '.',
                precision: 1,
                allowZero: true,
                allowNegative: false
            });

            // Inicializa os campos
            $('.money').maskMoney('mask');
            $('.percent-input').maskMoney('mask');

            // Função para alternar entre porcentagem e valor fixo
            function setupInputType(inputName, radioName) {
                $(`input[name="${radioName}"]`).change(function() {
                    const isPercent = $(this).val() === 'porcentagem';
                    const input = $(`input[name="${inputName}"]`);
                    const unitText = $(input).closest('.input-group').find('.unit-text');

                    if (isPercent) {
                        input.removeClass('money-input').addClass('percent-input');
                        input.maskMoney('destroy');
                        input.maskMoney({
                            suffix: '',
                            thousands: '',
                            decimal: '.',
                            precision: 1,
                            allowZero: true,
                            allowNegative: false
                        });
                        unitText.text('%');
                    } else {
                        input.removeClass('percent-input').addClass('money-input');
                        input.maskMoney('destroy');
                        input.maskMoney({
                            prefix: '',
                            thousands: ' ',
                            decimal: '.',
                            precision: 2,
                            allowZero: true,
                            allowNegative: false
                        });
                        unitText.text('R$');
                    }

                    input.maskMoney('mask');
                });
            }

            // Configura os switches para cada campo
            setupInputType('perc_deducoes', 'tipo_deducoes');
            setupInputType('perc_honorarios', 'tipo_honorarios');
            setupInputType('valor_oferta', 'tipo_oferta');
            setupInputType('perc_taxa', 'tipo_taxa');
            setupInputType('perc_taxa', 'tipo_taxa');
        });
    </script>
</body>
</html>